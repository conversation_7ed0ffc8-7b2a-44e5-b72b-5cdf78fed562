<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=1" />
<link href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAn1BMVEUAAADCAAAAAAA3yDfUAAA3yDfUAAA8PDzr6+sAAAD4+Pg3yDeQkJDTAADt7e3V1dU3yDdCQkIAAADbMTHUAABBykHUAAA2yDY3yDfr6+vTAAB3diDR0dGYcHDUAAAjhiPSAAA3yDeuAADUAAA3yDf////OCALg9+BLzktBuzRelimzKgv87+/dNTVflSn1/PWz6rO126g5yDlYniy0KgwjJ0TyAAAAI3RSTlMABAj0WD6rJcsN7X1HzMqUJyYW+/X08+bltqSeaVRBOy0cE+citBEAAADBSURBVDjLlczXEoIwFIThJPYGiL0XiL3r+z+bBOJs9JDMuLffP8v+Gxfc6aIyDQVjQcnqnvRDEQwLJYtXpZT+YhDHKIjLbS+OUeT4TjkKi6OwOArq+yeKXD9uDqQQbcOjyCy0e6bTojZSftX+U6zUQ7OuittDu1k0WHqRFfdXQijgjKfF6ZwAikvmKD6OQjmKWUcDigkztm5FZN05nMON9ZcoinlBmTNnAUdBnRbUUbgdBZwWbkcBpwXcVsBtxfjb31j1QB5qeebOAAAAAElFTkSuQmCC" rel="icon" type="image/x-icon" />
<title>Summary - Coverage Report</title>
<link rel="stylesheet" type="text/css" href="report.css" />
</head><body><div class="container"><div class="containerleft">
<h1>Summary<a class="button" href="https://github.com/danielpalme/ReportGenerator" title="Star on GitHub"><i class="icon-star"></i>Star</a><a class="button" href="https://github.com/sponsors/danielpalme" title="Become a sponsor"><i class="icon-sponsor"></i>Sponsor</a></h1>
<div class="card-group">
<div class="card">
<div class="card-header">Information</div>
<div class="card-body">
<div class="table">
<table>
<tr>
<th>Parser:</th>
<td class="limit-width " title="MultiReport (8x Cobertura)">MultiReport (8x Cobertura)</td>
</tr>
<tr>
<th>Assemblies:</th>
<td class="limit-width right" title="1">1</td>
</tr>
<tr>
<th>Classes:</th>
<td class="limit-width right" title="28">28</td>
</tr>
<tr>
<th>Files:</th>
<td class="limit-width right" title="14">14</td>
</tr>
<tr>
<th>Coverage date:</th>
<td class="limit-width " title="2025/6/12 - 9:53:48 - 2025/6/12 - 10:30:51">2025/6/12 - 9:53:48 - 2025/6/12 - 10:30:51</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Line coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar45">54%</div>
<div class="table">
<table>
<tr>
<th>Covered lines:</th>
<td class="limit-width right" title="794">794</td>
</tr>
<tr>
<th>Uncovered lines:</th>
<td class="limit-width right" title="653">653</td>
</tr>
<tr>
<th>Coverable lines:</th>
<td class="limit-width right" title="1447">1447</td>
</tr>
<tr>
<th>Total lines:</th>
<td class="limit-width right" title="3621">3621</td>
</tr>
<tr>
<th>Line coverage:</th>
<td class="limit-width right" title="794 of 1447">54.8%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Branch coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar53">47%</div>
<div class="table">
<table>
<tr>
<th>Covered branches:</th>
<td class="limit-width right" title="207">207</td>
</tr>
<tr>
<th>Total branches:</th>
<td class="limit-width right" title="436">436</td>
</tr>
<tr>
<th>Branch coverage:</th>
<td class="limit-width right" title="207 of 436">47.4%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Method coverage</div>
<div class="card-body">
<div class="center">
<p>Feature is only available for sponsors</p>
<a class="pro-button" href="https://reportgenerator.io/pro" target="_blank">Upgrade to PRO version</a>
</div>
</div>
</div>
</div>
<h1>Risk Hotspots</h1>
<risk-hotspots>
<div class="table-responsive">
<table class="overview table-fixed stripped">
<colgroup>
<col class="column-min-200" />
<col class="column-min-200" />
<col class="column-min-200" />
<col class="column105" />
<col class="column105" />
</colgroup>
<thead><tr><th>Assembly</th>
<th>Class</th>
<th>Method</th>
<th>Crap Score <a href="https://googletesting.blogspot.de/2011/02/this-code-is-crap.html" target="_blank"><i class="icon-info-circled"></i></a></th>
<th>Cyclomatic complexity <a href="https://en.wikipedia.org/wiki/Cyclomatic_complexity" target="_blank"><i class="icon-info-circled"></i></a></th>
</tr></thead>
<tbody>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="SetRtsFlowControl(System.Boolean)"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line215">SetRtsFlowControl(...)</a></td><td class="lightred right">272</td>
<td class="lightred right">16</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlStatistics.html">Alicres.SerialPort.Models.FlowControlStatistics</a></td>
<td title="GetPerformanceSuggestions()"><a href="Alicres.SerialPort_FlowControlStatistics.html#file0_line260">GetPerformanceSuggestions()</a></td><td class="lightred right">156</td>
<td class="lightgreen right">12</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="CanSend(System.Int32)"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line103">CanSend(...)</a></td><td class="lightred right">133</td>
<td class="lightred right">26</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_AdvancedBufferManager.html">Alicres.SerialPort.Services.AdvancedBufferManager</a></td>
<td title="PerformCleanup(System.Object)"><a href="Alicres.SerialPort_AdvancedBufferManager.html#file0_line249">PerformCleanup(...)</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="MonitorFlowControl(System.Object)"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line303">MonitorFlowControl(...)</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortService.html">Alicres.SerialPort.Services.SerialPortService</a></td>
<td title="ReconnectAsync()"><a href="Alicres.SerialPort_SerialPortService.html#file0_line695">ReconnectAsync()</a></td><td class="lightred right">72</td>
<td class="lightgreen right">8</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlStatistics.html">Alicres.SerialPort.Models.FlowControlStatistics</a></td>
<td title="GetDetailedReport()"><a href="Alicres.SerialPort_FlowControlStatistics.html#file0_line233">GetDetailedReport()</a></td><td class="lightred right">42</td>
<td class="lightgreen right">6</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortErrorEventArgs.2.html">Alicres.SerialPort.Models.SerialPortErrorEventArgs</a></td>
<td title=".ctor(System.String,System.Exception,System.String)"><a href="Alicres.SerialPort_SerialPortErrorEventArgs.2.html#file0_line114">.ctor(...)</a></td><td class="lightred right">42</td>
<td class="lightgreen right">6</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortStatus.html">Alicres.SerialPort.Models.SerialPortStatus</a></td>
<td title="UpdateConnectionState(Alicres.SerialPort.Models.SerialPortConnectionState)"><a href="Alicres.SerialPort_SerialPortStatus.html#file0_line114">UpdateConnectionState(...)</a></td><td class="lightred right">42</td>
<td class="lightgreen right">6</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="RecordReceive(System.Int32)"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line164">RecordReceive(...)</a></td><td class="lightred right">42</td>
<td class="lightgreen right">6</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortService.html">Alicres.SerialPort.Services.SerialPortService</a></td>
<td title="OnDataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)"><a href="Alicres.SerialPort_SerialPortService.html#file0_line581">OnDataReceived(...)</a></td><td class="lightred right">42</td>
<td class="lightgreen right">6</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_AdvancedBufferManager.html">Alicres.SerialPort.Services.AdvancedBufferManager</a></td>
<td title="HandleBufferOverflow(Alicres.SerialPort.Models.SerialPortData)"><a href="Alicres.SerialPort_AdvancedBufferManager.html#file0_line204">HandleBufferOverflow(...)</a></td><td class="lightred right">37</td>
<td class="lightred right">20</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="ProcessFlowControlData(System.Byte[])"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line177">ProcessFlowControlData(...)</a></td><td class="lightgreen right">26</td>
<td class="lightred right">26</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortConfiguration.html">Alicres.SerialPort.Models.SerialPortConfiguration</a></td>
<td title="IsValid()"><a href="Alicres.SerialPort_SerialPortConfiguration.html#file0_line116">IsValid()</a></td><td class="lightgreen right">18</td>
<td class="lightred right">18</td>
</tr>
</tbody>
</table>
</div>
</risk-hotspots>
<h1>Coverage</h1>
<coverage-info>
<div class="table-responsive">
<table class="overview table-fixed stripped">
<colgroup>
<col class="column-min-200" />
<col class="column90" />
<col class="column105" />
<col class="column100" />
<col class="column70" />
<col class="column60" />
<col class="column112" />
<col class="column90" />
<col class="column70" />
<col class="column60" />
<col class="column112" />
</colgroup>
<thead>
<tr class="header"><th></th><th colspan="6" class="center">Line coverage</th><th colspan="4" class="center">Branch coverage</th></tr>
<tr><th>Name</th><th class="right">Covered</th><th class="right">Uncovered</th><th class="right">Coverable</th><th class="right">Total</th><th class="center" colspan="2">Percentage</th><th class="right">Covered</th><th class="right">Total</th><th class="center" colspan="2">Percentage</th></tr></thead>
<tbody>
<tr><th>Alicres.SerialPort</th><th class="right">794</th><th class="right">653</th><th class="right">1447</th><th class="right">5967</th><th title="794/1447" class="right">54.8%</th><th><table class="coverage"><tr><td class="green covered55">&nbsp;</td><td class="red covered45">&nbsp;</td></tr></table></th><th class="right">207</th><th class="right">436</th><th class="right" title="207/436">47.4%</th><th><table class="coverage"><tr><td class="green covered47">&nbsp;</td><td class="red covered53">&nbsp;</td></tr></table></th></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortConfigurationException.html">Alicres.SerialPort.Exceptions.SerialPortConfigurationException</a></td><td class="right">15</td><td class="right">0</td><td class="right">15</td><td class="right">204</td><td title="15/15" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortConnectionException.html">Alicres.SerialPort.Exceptions.SerialPortConnectionException</a></td><td class="right">15</td><td class="right">0</td><td class="right">15</td><td class="right">204</td><td title="15/15" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortDataException.html">Alicres.SerialPort.Exceptions.SerialPortDataException</a></td><td class="right">15</td><td class="right">0</td><td class="right">15</td><td class="right">204</td><td title="15/15" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortException.html">Alicres.SerialPort.Exceptions.SerialPortException</a></td><td class="right">18</td><td class="right">0</td><td class="right">18</td><td class="right">204</td><td title="18/18" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_GlobalReconnectOptions.html">Alicres.SerialPort.Extensions.GlobalReconnectOptions</a></td><td class="right">3</td><td class="right">0</td><td class="right">3</td><td class="right">96</td><td title="3/3" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortServiceOptions.html">Alicres.SerialPort.Extensions.SerialPortServiceOptions</a></td><td class="right">4</td><td class="right">0</td><td class="right">4</td><td class="right">96</td><td title="4/4" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_ServiceCollectionExtensions.html">Alicres.SerialPort.Extensions.ServiceCollectionExtensions</a></td><td class="right">13</td><td class="right">0</td><td class="right">13</td><td class="right">96</td><td title="13/13" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortAddedEventArgs.html">Alicres.SerialPort.Interfaces.SerialPortAddedEventArgs</a></td><td class="right">4</td><td class="right">1</td><td class="right">5</td><td class="right">159</td><td title="4/5" class="right">80%</td><td><table class="coverage"><tr><td class="green covered80">&nbsp;</td><td class="red covered20">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortDataReceivedEventArgs.html">Alicres.SerialPort.Interfaces.SerialPortDataReceivedEventArgs</a></td><td class="right">0</td><td class="right">5</td><td class="right">5</td><td class="right">129</td><td title="0/5" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortErrorEventArgs.html">Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs</a></td><td class="right">0</td><td class="right">9</td><td class="right">9</td><td class="right">129</td><td title="0/9" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortRemovedEventArgs.html">Alicres.SerialPort.Interfaces.SerialPortRemovedEventArgs</a></td><td class="right">4</td><td class="right">1</td><td class="right">5</td><td class="right">159</td><td title="4/5" class="right">80%</td><td><table class="coverage"><tr><td class="green covered80">&nbsp;</td><td class="red covered20">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortStatusChangedEventArgs.html">Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs</a></td><td class="right">0</td><td class="right">9</td><td class="right">9</td><td class="right">129</td><td title="0/9" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_BufferOverflowEventArgs.html">Alicres.SerialPort.Models.BufferOverflowEventArgs</a></td><td class="right">14</td><td class="right">0</td><td class="right">14</td><td class="right">190</td><td title="14/14" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">2</td><td class="right">2</td><td class="right" title="2/2">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_BufferStatistics.html">Alicres.SerialPort.Models.BufferStatistics</a></td><td class="right">64</td><td class="right">3</td><td class="right">67</td><td class="right">295</td><td title="64/67" class="right">95.5%</td><td><table class="coverage"><tr><td class="green covered96">&nbsp;</td><td class="red covered4">&nbsp;</td></tr></table></td><td class="right">21</td><td class="right">22</td><td class="right" title="21/22">95.4%</td><td><table class="coverage"><tr><td class="green covered95">&nbsp;</td><td class="red covered5">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_BufferWarningEventArgs.html">Alicres.SerialPort.Models.BufferWarningEventArgs</a></td><td class="right">14</td><td class="right">0</td><td class="right">14</td><td class="right">190</td><td title="14/14" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_CongestionDetectedEventArgs.html">Alicres.SerialPort.Models.CongestionDetectedEventArgs</a></td><td class="right">14</td><td class="right">0</td><td class="right">14</td><td class="right">190</td><td title="14/14" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_FlowControlStatistics.html">Alicres.SerialPort.Models.FlowControlStatistics</a></td><td class="right">11</td><td class="right">57</td><td class="right">68</td><td class="right">295</td><td title="11/68" class="right">16.1%</td><td><table class="coverage"><tr><td class="green covered16">&nbsp;</td><td class="red covered84">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">20</td><td class="right" title="0/20">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_FlowControlStatusChangedEventArgs.html">Alicres.SerialPort.Models.FlowControlStatusChangedEventArgs</a></td><td class="right">12</td><td class="right">0</td><td class="right">12</td><td class="right">190</td><td title="12/12" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortConfiguration.html">Alicres.SerialPort.Models.SerialPortConfiguration</a></td><td class="right">37</td><td class="right">0</td><td class="right">37</td><td class="right">140</td><td title="37/37" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">18</td><td class="right">18</td><td class="right" title="18/18">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortData.html">Alicres.SerialPort.Models.SerialPortData</a></td><td class="right">56</td><td class="right">0</td><td class="right">56</td><td class="right">167</td><td title="56/56" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">18</td><td class="right">20</td><td class="right" title="18/20">90%</td><td><table class="coverage"><tr><td class="green covered90">&nbsp;</td><td class="red covered10">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortDataReceivedEventArgs.2.html">Alicres.SerialPort.Models.SerialPortDataReceivedEventArgs</a></td><td class="right">0</td><td class="right">8</td><td class="right">8</td><td class="right">130</td><td title="0/8" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">2</td><td class="right" title="0/2">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortErrorEventArgs.2.html">Alicres.SerialPort.Models.SerialPortErrorEventArgs</a></td><td class="right">0</td><td class="right">14</td><td class="right">14</td><td class="right">130</td><td title="0/14" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">6</td><td class="right" title="0/6">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortStatus.html">Alicres.SerialPort.Models.SerialPortStatus</a></td><td class="right">12</td><td class="right">32</td><td class="right">44</td><td class="right">169</td><td title="12/44" class="right">27.2%</td><td><table class="coverage"><tr><td class="green covered27">&nbsp;</td><td class="red covered73">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">6</td><td class="right" title="0/6">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortStatusChangedEventArgs.2.html">Alicres.SerialPort.Models.SerialPortStatusChangedEventArgs</a></td><td class="right">0</td><td class="right">14</td><td class="right">14</td><td class="right">130</td><td title="0/14" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">2</td><td class="right" title="0/2">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_AdvancedBufferManager.html">Alicres.SerialPort.Services.AdvancedBufferManager</a></td><td class="right">115</td><td class="right">36</td><td class="right">151</td><td class="right">310</td><td title="115/151" class="right">76.1%</td><td><table class="coverage"><tr><td class="green covered76">&nbsp;</td><td class="red covered24">&nbsp;</td></tr></table></td><td class="right">44</td><td class="right">76</td><td class="right" title="44/76">57.8%</td><td><table class="coverage"><tr><td class="green covered58">&nbsp;</td><td class="red covered42">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td><td class="right">110</td><td class="right">63</td><td class="right">173</td><td class="right">353</td><td title="110/173" class="right">63.5%</td><td><table class="coverage"><tr><td class="green covered64">&nbsp;</td><td class="red covered36">&nbsp;</td></tr></table></td><td class="right">56</td><td class="right">118</td><td class="right" title="56/118">47.4%</td><td><table class="coverage"><tr><td class="green covered47">&nbsp;</td><td class="red covered53">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortManager.html">Alicres.SerialPort.Services.SerialPortManager</a></td><td class="right">102</td><td class="right">95</td><td class="right">197</td><td class="right">445</td><td title="102/197" class="right">51.7%</td><td><table class="coverage"><tr><td class="green covered52">&nbsp;</td><td class="red covered48">&nbsp;</td></tr></table></td><td class="right">20</td><td class="right">38</td><td class="right" title="20/38">52.6%</td><td><table class="coverage"><tr><td class="green covered53">&nbsp;</td><td class="red covered47">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortService.html">Alicres.SerialPort.Services.SerialPortService</a></td><td class="right">142</td><td class="right">306</td><td class="right">448</td><td class="right">834</td><td title="142/448" class="right">31.6%</td><td><table class="coverage"><tr><td class="green covered32">&nbsp;</td><td class="red covered68">&nbsp;</td></tr></table></td><td class="right">28</td><td class="right">106</td><td class="right" title="28/106">26.4%</td><td><table class="coverage"><tr><td class="green covered26">&nbsp;</td><td class="red covered74">&nbsp;</td></tr></table></td></tr>
</tbody>
</table>
</div>
</coverage-info>
<div class="footer">Generated by: ReportGenerator *******<br />2025/6/12 - 10:31:03<br /><a href="https://github.com/danielpalme/ReportGenerator">GitHub</a> | <a href="https://reportgenerator.io">reportgenerator.io</a></div></div></div>
<script type="text/javascript">
/* <![CDATA[ */
(function() {
    var url = window.location.href;
    var startOfQueryString = url.indexOf('?');
    var queryString = startOfQueryString > -1 ? url.substr(startOfQueryString) : '';

    if (startOfQueryString > -1) {
        var i = 0, href= null;
        var css = document.getElementsByTagName('link');

        for (i = 0; i < css.length; i++) {
            if (css[i].getAttribute('rel') !== 'stylesheet') {
            continue;
            }

            href = css[i].getAttribute('href');

            if (href) {
            css[i].setAttribute('href', href + queryString);
            }
        }

        var links = document.getElementsByTagName('a');

        for (i = 0; i < links.length; i++) {
            href = links[i].getAttribute('href');

            if (href
                && !href.startsWith('http://')
                && !href.startsWith('https://')
                && !href.startsWith('#')
                && href.indexOf('?') === -1) {
            links[i].setAttribute('href', href + queryString);
            }
        }
    }

    var newScript = document.createElement('script');
    newScript.src = 'main.js' + queryString;
    document.getElementsByTagName('body')[0].appendChild(newScript);
})();
/* ]]> */ 
</script></body></html>